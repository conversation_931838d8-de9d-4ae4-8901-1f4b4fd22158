/**
 * Standard-to-Custom Field Mapping Management
 *
 * Provides management utilities for standard-to-custom field mappings including
 * auto-discovery, validation, creation, and maintenance operations. Integrates
 * with the configuration system and provides a high-level interface for
 * managing AP standard field to CC custom field mappings.
 *
 * @fileoverview Management utilities for standard-to-custom field mappings
 * @version 1.0.0
 * @since 2024-08-04
 */

import apiClient from "@apiClient";
import { getConfig } from "@/utils/configs";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import {
	getStandardToCustomMappings,
	storeStandardToCustomMapping,
} from "../database/operations";
import {
	discoverStandardToCustomMappings,
	type AutoDiscoveryResult,
	type DiscoveryConfig,
} from "../discovery/standardFieldMatcher";
import type { StandardToCustomMapping } from "@/config/standardFieldMappings";

/**
 * Management operation result
 */
export interface MappingManagementResult {
	success: boolean;
	message: string;
	mappingsProcessed: number;
	mappingsCreated: number;
	mappingsSkipped: number;
	errors: string[];
	warnings: string[];
	details?: {
		discoveryResult?: AutoDiscoveryResult;
		existingMappings?: StandardToCustomMapping[];
		newMappings?: StandardToCustomMapping[];
	};
}

/**
 * Auto-discovery and setup options
 */
export interface AutoSetupOptions {
	/** Whether to automatically create discovered mappings */
	autoCreate: boolean;
	/** Override confidence threshold */
	confidenceThreshold?: number;
	/** Include medium confidence matches */
	includeMediumConfidence?: boolean;
	/** Dry run mode - analyze only, don't create mappings */
	dryRun?: boolean;
	/** Force recreation of existing mappings */
	forceRecreate?: boolean;
}

/**
 * Perform auto-discovery and setup of standard-to-custom field mappings
 *
 * Analyzes CliniCore custom fields to identify potential mappings to AutoPatient
 * standard fields and optionally creates the mappings automatically.
 *
 * @param options - Auto-setup configuration options
 * @returns Management operation result
 *
 * @example
 * ```typescript
 * // Dry run to see what would be discovered
 * const dryRunResult = await autoDiscoverAndSetupMappings({
 *   autoCreate: false,
 *   dryRun: true
 * });
 * 
 * // Actually create the mappings
 * const setupResult = await autoDiscoverAndSetupMappings({
 *   autoCreate: true,
 *   confidenceThreshold: 0.8
 * });
 * ```
 *
 * @since 1.0.0
 */
export async function autoDiscoverAndSetupMappings(
	options: AutoSetupOptions = {},
): Promise<MappingManagementResult> {
	const result: MappingManagementResult = {
		success: false,
		message: "",
		mappingsProcessed: 0,
		mappingsCreated: 0,
		mappingsSkipped: 0,
		errors: [],
		warnings: [],
		details: {},
	};

	try {
		logInfo("Starting auto-discovery and setup of standard-to-custom mappings", {
			options,
		});

		// Check if feature is enabled
		const isEnabled = getConfig("enableStandardToCustomMapping");
		if (!isEnabled) {
			result.message = "Standard-to-custom field mapping is disabled in configuration";
			result.warnings.push(result.message);
			logWarn(result.message);
			return result;
		}

		// Get configuration
		const confidenceThreshold = options.confidenceThreshold ?? 
			getConfig("standardMappingConfidenceThreshold");
		const includeMediumConfidence = options.includeMediumConfidence ?? 
			getConfig("includeMediumConfidenceMappings");

		// Fetch CC custom fields
		const ccFields = await apiClient.cc.ccCustomfieldReq.all();
		if (ccFields.length === 0) {
			result.message = "No CliniCore custom fields found for analysis";
			result.warnings.push(result.message);
			logWarn(result.message);
			return result;
		}

		// Configure discovery
		const discoveryConfig: DiscoveryConfig = {
			confidenceThreshold,
			includeMediumConfidence,
			analyzeDescriptions: true,
			excludePatterns: [
				"^(test_|dev_|debug_)",
				"^(internal_|system_)",
				"^(temp_|tmp_)",
				"^(legacy_|deprecated_)",
			],
		};

		// Perform auto-discovery
		const discoveryResult = await discoverStandardToCustomMappings(
			ccFields,
			discoveryConfig,
		);
		result.details!.discoveryResult = discoveryResult;

		logInfo("Auto-discovery completed", {
			suggestedMappings: discoveryResult.suggestedMappings.length,
			fieldsAnalyzed: discoveryResult.statistics.fieldsAnalyzed,
		});

		if (discoveryResult.suggestedMappings.length === 0) {
			result.message = "No suitable mappings discovered";
			result.success = true; // Not an error, just no mappings found
			return result;
		}

		// Get existing mappings
		const existingMappings = await getStandardToCustomMappings();
		result.details!.existingMappings = existingMappings;

		// Filter out existing mappings unless force recreate is enabled
		const newMappings = discoveryResult.suggestedMappings.filter(mapping => {
			const exists = existingMappings.some(existing => 
				existing.apStandardField === mapping.apStandardField &&
				existing.ccFieldId === mapping.ccFieldId
			);
			
			if (exists && !options.forceRecreate) {
				result.mappingsSkipped++;
				return false;
			}
			
			return true;
		});

		result.details!.newMappings = newMappings;
		result.mappingsProcessed = discoveryResult.suggestedMappings.length;

		if (options.dryRun) {
			result.message = `Dry run completed: ${newMappings.length} mappings would be created`;
			result.success = true;
			logInfo(result.message, {
				totalDiscovered: discoveryResult.suggestedMappings.length,
				newMappings: newMappings.length,
				skipped: result.mappingsSkipped,
			});
			return result;
		}

		// Create mappings if auto-create is enabled
		if (options.autoCreate && newMappings.length > 0) {
			for (const mapping of newMappings) {
				try {
					await storeStandardToCustomMapping(
						mapping.apStandardField,
						mapping.ccConfig!,
					);
					result.mappingsCreated++;
					
					logDebug("Created standard-to-custom mapping", {
						apStandardField: mapping.apStandardField,
						ccCustomField: mapping.ccCustomField,
						ccFieldId: mapping.ccFieldId,
					});
				} catch (error) {
					const errorMessage = error instanceof Error ? error.message : String(error);
					result.errors.push(
						`Failed to create mapping ${mapping.apStandardField} -> ${mapping.ccCustomField}: ${errorMessage}`,
					);
					logError("Failed to create standard-to-custom mapping", {
						mapping,
						error: errorMessage,
					});
				}
			}
		}

		// Generate result message
		if (options.autoCreate) {
			result.message = `Successfully created ${result.mappingsCreated} standard-to-custom mappings`;
			if (result.mappingsSkipped > 0) {
				result.message += ` (${result.mappingsSkipped} skipped as existing)`;
			}
		} else {
			result.message = `Discovery completed: ${newMappings.length} mappings available for creation`;
		}

		result.success = result.errors.length === 0;

		logInfo("Auto-discovery and setup completed", {
			success: result.success,
			mappingsCreated: result.mappingsCreated,
			mappingsSkipped: result.mappingsSkipped,
			errors: result.errors.length,
		});

	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		result.errors.push(`Auto-discovery failed: ${errorMessage}`);
		result.message = `Auto-discovery failed: ${errorMessage}`;
		
		logError("Auto-discovery and setup failed", {
			error: errorMessage,
			options,
		});
	}

	return result;
}

/**
 * Validate existing standard-to-custom mappings
 *
 * Checks existing mappings for validity, including whether the CC custom fields
 * still exist and whether the mappings are still appropriate.
 *
 * @returns Validation result with details about mapping status
 *
 * @since 1.0.0
 */
export async function validateExistingMappings(): Promise<MappingManagementResult> {
	const result: MappingManagementResult = {
		success: false,
		message: "",
		mappingsProcessed: 0,
		mappingsCreated: 0,
		mappingsSkipped: 0,
		errors: [],
		warnings: [],
	};

	try {
		logInfo("Starting validation of existing standard-to-custom mappings");

		// Get existing mappings
		const existingMappings = await getStandardToCustomMappings();
		if (existingMappings.length === 0) {
			result.message = "No existing standard-to-custom mappings found";
			result.success = true;
			return result;
		}

		// Get current CC custom fields
		const ccFields = await apiClient.cc.ccCustomfieldReq.all();
		const ccFieldMap = new Map(ccFields.map(field => [field.id, field]));

		result.mappingsProcessed = existingMappings.length;

		// Validate each mapping
		for (const mapping of existingMappings) {
			const ccField = ccFieldMap.get(mapping.ccFieldId!);
			
			if (!ccField) {
				result.errors.push(
					`CC custom field not found for mapping: ${mapping.apStandardField} -> ID ${mapping.ccFieldId}`,
				);
			} else if (ccField.name !== mapping.ccCustomField) {
				result.warnings.push(
					`CC custom field name changed: ${mapping.ccCustomField} -> ${ccField.name} (ID: ${mapping.ccFieldId})`,
				);
			}
		}

		result.success = result.errors.length === 0;
		result.message = result.success 
			? `All ${existingMappings.length} mappings are valid`
			: `Found ${result.errors.length} invalid mappings out of ${existingMappings.length}`;

		logInfo("Mapping validation completed", {
			totalMappings: existingMappings.length,
			errors: result.errors.length,
			warnings: result.warnings.length,
		});

	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		result.errors.push(`Validation failed: ${errorMessage}`);
		result.message = `Validation failed: ${errorMessage}`;
		
		logError("Mapping validation failed", {
			error: errorMessage,
		});
	}

	return result;
}

/**
 * Get comprehensive mapping status and statistics
 *
 * @returns Current status of standard-to-custom field mappings
 *
 * @since 1.0.0
 */
export async function getMappingStatus(): Promise<{
	enabled: boolean;
	existingMappings: number;
	ccFieldsAvailable: number;
	apStandardFields: number;
	lastValidation?: Date;
}> {
	try {
		const [existingMappings, ccFields] = await Promise.all([
			getStandardToCustomMappings(),
			apiClient.cc.ccCustomfieldReq.all(),
		]);

		return {
			enabled: getConfig("enableStandardToCustomMapping"),
			existingMappings: existingMappings.length,
			ccFieldsAvailable: ccFields.length,
			apStandardFields: Array.from(require("@/config/standardFieldMappings").AP_STANDARD_FIELDS).length,
		};
	} catch (error) {
		logError("Failed to get mapping status", { error });
		return {
			enabled: false,
			existingMappings: 0,
			ccFieldsAvailable: 0,
			apStandardFields: 0,
		};
	}
}
