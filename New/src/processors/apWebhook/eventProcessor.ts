/**
 * AutoPatient Contact Webhook Event Processor
 *
 * Main event processing orchestrator for AutoPatient contact webhook events.
 * Handles event filtering, validation, contact synchronization to CliniCore,
 * and database operations with comprehensive error handling and logging.
 *
 * **Processing Flow:**
 * 1. Initial validation: Extract contact_id and check if calendar field exists
 * 2. Fetch full contact data: Use AP API to get complete contact details
 * 3. Database lookup: Search for existing patient by apId, email, phone
 * 4. Sync buffer check: Compare dateUpdated from AP API with database apUpdatedAt
 * 5. CliniCore patient processing: Search for existing CC patient, then create/update
 * 6. Database synchronization: Update local database with latest data and timestamps
 * 7. Return comprehensive processing results
 *
 * @fileoverview Main event processor for AutoPatient contact webhooks
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { GetAPContactType } from "@type";
import { contactReq } from "@/apiClient";
import { logDatabaseError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import {
	checkSyncBuffer,
	createPatientR<PERSON>ord,
	lookupExistingPatient,
	updatePatientR<PERSON>ord,
	upsertCcPatient,
} from "./contactSynchronizer";
import { mapApContactToCcPatient } from "./fieldMapper";
import type {
	APContactCreationWebhookPayload,
	ContactSyncResult,
	EventProcessingContext,
	EventProcessingResult,
	PatientSelect,
	WebhookProcessingConfig,
} from "./types";

/**
 * Process AutoPatient contact webhook event
 *
 * Main entry point for processing AutoPatient contact webhook events. Follows
 * the exact same pattern as CliniCore webhook handler but in reverse direction.
 *
 * **Processing Flow:**
 * 1. Initial validation: Check if calendar field exists (skip if present)
 * 2. Fetch full contact data from AutoPatient API
 * 3. Database lookup with multiple fallback criteria
 * 4. Sync buffer check using AP API dateUpdated vs database apUpdatedAt
 * 5. CliniCore patient processing (search and upsert)
 * 6. Database synchronization with proper timestamps
 * 7. Return comprehensive processing results
 *
 * @param payload - AutoPatient contact webhook payload
 * @param config - Optional processing configuration overrides
 * @returns Complete event processing result with success/error details
 *
 * @example
 * ```typescript
 * const result = await processContactWebhookEvent(webhookPayload);
 * if (result.success) {
 *   console.log(`Processed contact ${result.contactId} in ${result.metadata.durationMs}ms`);
 * } else {
 *   console.error(`Processing failed: ${result.error?.message}`);
 * }
 * ```
 */
export async function processContactWebhookEvent(
	payload: APContactCreationWebhookPayload,
	config?: Partial<WebhookProcessingConfig>,
): Promise<EventProcessingResult> {
	const startTime = new Date();

	try {
		// Step 1: Initial validation - check for calendar field
		const filterResult = filterWebhookEvent(payload);
		if (!filterResult.shouldProcess) {
			return createSkippedResult(payload, startTime, filterResult.reason);
		}

		// Step 2: Fetch full contact data from AutoPatient API
		const apContact = await fetchFullContactData(payload.contact_id);
		if (!apContact) {
			return createErrorResult(
				payload,
				startTime,
				"Failed to fetch full contact data from AutoPatient API",
				"api_fetch",
			);
		}

		// Create processing context
		const context = createProcessingContext(payload, config);

		// Process the contact event with full contact data
		const processingResult = await processContactEvent(
			payload,
			apContact,
			context,
		);

		// Calculate final processing time
		const endTime = new Date();
		const durationMs = endTime.getTime() - startTime.getTime();

		// Update result with final metadata
		processingResult.metadata = {
			...processingResult.metadata,
			durationMs,
			endTime,
		};

		logInfo(
			`AP contact webhook processing completed: ${processingResult.success ? "success" : "failed"} ` +
				`(${durationMs}ms)`,
		);

		return processingResult;
	} catch (error) {
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"webhook_processing",
		);

		logError("Unexpected error in AP contact webhook processing", error);

		return createErrorResult(
			payload,
			startTime,
			error instanceof Error ? error.message : String(error),
			"processing",
		);
	}
}

/**
 * Filter webhook event to determine if it should be processed
 *
 * Checks if the webhook event should be processed based on the presence of
 * calendar field. Events with calendar field are appointment-related and
 * should be skipped for contact processing.
 *
 * @param payload - Webhook payload
 * @returns Filter result with processing decision
 */
function filterWebhookEvent(payload: APContactCreationWebhookPayload): {
	shouldProcess: boolean;
	reason: string;
} {
	// Check if calendar field exists - skip if present (appointment event)
	if ("calendar" in payload && payload.calendar !== undefined) {
		logInfo(
			"Skipping webhook event - calendar field present (appointment-related event)",
		);
		return {
			shouldProcess: false,
			reason: "Calendar field present - appointment-related event",
		};
	}

	logDebug("Webhook event passed filtering - pure contact event");
	return {
		shouldProcess: true,
		reason: "Pure contact event",
	};
}

/**
 * Fetch full contact data from AutoPatient API
 *
 * Uses the contact_id from the webhook to fetch complete contact details
 * from the AutoPatient API, including the dateUpdated field needed for
 * sync buffer comparison.
 *
 * @param contactId - AutoPatient contact ID
 * @returns Full contact data or null if failed
 */
async function fetchFullContactData(
	contactId: string,
): Promise<GetAPContactType | null> {
	try {
		logDebug(`Fetching full contact data for AP ID: ${contactId}`);

		const apContact = await contactReq.get(contactId, true); // invalidate cache

		if (!apContact) {
			logError(`No contact data returned from AP API for ID: ${contactId}`);
			return null;
		}

		logInfo(`Successfully fetched full contact data for AP ID: ${contactId}`);

		return apContact;
	} catch (error) {
		logError(
			`Failed to fetch contact data from AP API for ID: ${contactId}`,
			error,
		);
		return null;
	}
}

/**
 * Process contact event with full contact data
 *
 * Handles contact processing with complete synchronization to CliniCore,
 * following the exact same pattern as CliniCore webhook handler.
 *
 * @param payload - Original webhook payload
 * @param apContact - Full contact data from AP API
 * @param context - Processing context
 * @returns Contact processing result with sync details
 */
async function processContactEvent(
	payload: APContactCreationWebhookPayload,
	apContact: GetAPContactType,
	context: EventProcessingContext,
): Promise<EventProcessingResult> {
	const startTime = new Date();

	logDebug(`Processing contact event for AP ID: ${payload.contact_id}`);

	try {
		// Step 1: Look up existing patient record (following CC pattern)
		const lookupResult = await lookupExistingPatient(
			payload.contact_id,
			apContact.email,
			apContact.phone || undefined,
		);

		// Step 2: Check sync buffer if patient exists (following CC pattern)
		if (lookupResult.found && lookupResult.patient) {
			const bufferCheck = checkSyncBuffer(
				apContact.dateUpdated,
				lookupResult.patient.apUpdatedAt,
				context.config.syncBufferSeconds || 60,
			);

			if (!bufferCheck.shouldProcess) {
				return createSkippedResult(payload, startTime, bufferCheck.reason);
			}
		}

		// Step 3: Map AP contact fields to CC patient format
		const fieldMapping = mapApContactToCcPatient(apContact);

		// Step 4: CliniCore patient processing (search and upsert)
		const ccResponse = await upsertCcPatient(
			fieldMapping.ccPatientData,
			lookupResult.patient?.ccId || undefined,
			apContact.email,
			apContact.phone,
		);

		if (!ccResponse.success || !ccResponse.patient) {
			return createErrorResult(
				payload,
				startTime,
				ccResponse.error || "Failed to upsert CC patient",
				"api_call",
			);
		}

		// Step 5: Update database record
		let dbPatient: PatientSelect | null;
		if (lookupResult.patient) {
			dbPatient = await updatePatientRecord(
				lookupResult.patient.id,
				apContact,
				ccResponse.patient,
			);
		} else {
			dbPatient = await createPatientRecord(apContact, ccResponse.patient);
		}

		if (!dbPatient) {
			logWarn("Failed to update database record, but CC sync succeeded");
		}

		// Create successful result
		const contactSync: ContactSyncResult = {
			action: lookupResult.patient ? "updated" : "created",
			apContact,
			ccPatient: ccResponse.patient,
			dbPatient: dbPatient || undefined,
		};

		const endTime = new Date();
		const durationMs = endTime.getTime() - startTime.getTime();

		return {
			success: true,
			event: "contact_created",
			contactId: payload.contact_id,
			contactSync,
			metadata: {
				durationMs,
				startTime,
				endTime,
			},
		};
	} catch (error) {
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"contact_processing",
		);

		logError("Error processing contact event", error);

		return createErrorResult(
			payload,
			startTime,
			error instanceof Error ? error.message : String(error),
			"contact_processing",
		);
	}
}

/**
 * Create processing context
 *
 * Creates the processing context with configuration and metadata for
 * webhook event processing.
 *
 * @param payload - Webhook payload
 * @param configOverrides - Configuration overrides
 * @returns Processing context
 */
function createProcessingContext(
	_payload: APContactCreationWebhookPayload,
	configOverrides?: Partial<WebhookProcessingConfig>,
): EventProcessingContext {
	const defaultConfig: WebhookProcessingConfig = {
		skipSyncBuffer: false,
		syncBufferSeconds: 60,
		createMissingContacts: true,
		updateExistingContacts: true,
	};

	return {
		config: { ...defaultConfig, ...configOverrides },
		startTime: new Date(),
	};
}

/**
 * Create error result
 *
 * Creates a standardized error result for failed processing.
 */
function createErrorResult(
	payload: APContactCreationWebhookPayload,
	startTime: Date,
	errorMessage: string,
	stage: string,
): EventProcessingResult {
	const endTime = new Date();
	const durationMs = endTime.getTime() - startTime.getTime();

	return {
		success: false,
		event: "contact_created",
		contactId: payload.contact_id,
		metadata: {
			durationMs,
			startTime,
			endTime,
		},
		error: {
			message: errorMessage,
			stage,
		},
	};
}

/**
 * Create skipped result
 *
 * Creates a standardized result for skipped processing.
 */
function createSkippedResult(
	payload: APContactCreationWebhookPayload,
	startTime: Date,
	_reason: string,
): EventProcessingResult {
	const endTime = new Date();
	const durationMs = endTime.getTime() - startTime.getTime();

	return {
		success: true,
		event: "contact_created",
		contactId: payload.contact_id,
		contactSync: {
			action: "skipped",
		},
		metadata: {
			durationMs,
			startTime,
			endTime,
		},
	};
}
