/**
 * Patient Custom Field Value Synchronization
 *
 * Main entry point for patient custom field value synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Provides the core functions
 * used by the admin endpoint for bidirectional value synchronization.
 *
 * @fileoverview Patient custom field value synchronization main functions
 * @version 1.0.0
 * @since 2024-07-29
 */

import { logError, logInfo, logWarn } from "@/utils/logger";
import {
	createMappingLookupTables,
	getAllFieldMappings,
} from "./fieldMappingResolver";
import {
	fetchApPatientData,
	fetchCcPatientData,
	updateApPatientCustomFields,
	updateCcPatientCustomFields,
} from "./patientDataFetcher";
import type {
	APCustomFieldValue,
	CCCustomFieldValue,
	FieldValueSyncResult,
	PatientCustomFieldSyncResult,
} from "./types";
import { convertApValueToCc, convertCcValueToAp } from "./valueConverters";

/**
 * Synchronize custom field values from AutoPatient to CliniCore
 *
 * Fetches patient data from AP, converts custom field values using field mappings,
 * and updates the corresponding patient in CC with the converted values.
 *
 * @param patientId - Local database patient ID
 * @param skipMissingFields - Whether to skip fields without mappings
 * @returns Synchronization result with detailed statistics
 */
export async function syncApToCcCustomFields(
	patientId: string,
	skipMissingFields = true,
): Promise<PatientCustomFieldSyncResult> {
	const startTime = Date.now();

	const result: PatientCustomFieldSyncResult = {
		success: false,
		patientId,
		targetPlatform: "cc",
		fieldsProcessed: 0,
		fieldsUpdated: 0,
		fieldsSkipped: 0,
		fieldsFailed: 0,
		results: [],
		errors: [],
		warnings: [],
		executionTimeMs: 0,
	};

	try {
		// Step 1: Fetch patient data from AP
		const apPatientData = await fetchApPatientData(patientId);
		if (!apPatientData) {
			result.errors.push("Failed to fetch patient data from AutoPatient");
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		const apCustomFields = apPatientData.customFields as APCustomFieldValue[];
		if (!apCustomFields || apCustomFields.length === 0) {
			logInfo("No custom fields found for AP patient", {
				patientId,
			});
			result.success = true;
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		// Step 2: Get field mappings
		const fieldMappings = await getAllFieldMappings();
		if (fieldMappings.length === 0) {
			const error = "No field mappings found in database";
			result.errors.push(error);
			logWarn(error, { patientId });
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		const { apToCC } = createMappingLookupTables(fieldMappings);

		// Step 3: Convert AP values to CC format
		const ccCustomFields: CCCustomFieldValue[] = [];
		const fieldResults: FieldValueSyncResult[] = [];

		for (const apField of apCustomFields) {
			result.fieldsProcessed++;

			const mapping = apToCC.get(apField.id);
			if (!mapping) {
				const fieldResult: FieldValueSyncResult = {
					fieldName: `AP Field ${apField.id}`,
					apFieldId: apField.id,
					success: false,
					action: skipMissingFields ? "skipped" : "failed",
					originalValue: apField.value,
					error: skipMissingFields
						? "No field mapping found"
						: "Missing field mapping",
				};

				fieldResults.push(fieldResult);

				if (skipMissingFields) {
					result.fieldsSkipped++;
				} else {
					result.fieldsFailed++;
					result.errors.push(`No mapping found for AP field ${apField.id}`);
				}
				continue;
			}

			// Convert value
			const conversionResult = convertApValueToCc(apField, mapping);

			const fieldResult: FieldValueSyncResult = {
				fieldName: mapping.ccConfig.name,
				apFieldId: apField.id,
				ccFieldId: mapping.ccId,
				success: conversionResult.success,
				action: conversionResult.success ? "updated" : "failed",
				originalValue: apField.value,
				convertedValue: conversionResult.convertedValue
					? (conversionResult.convertedValue as CCCustomFieldValue).values
					: null,
				error: conversionResult.error,
				warnings: conversionResult.warnings,
			};

			fieldResults.push(fieldResult);

			if (conversionResult.success && conversionResult.convertedValue) {
				ccCustomFields.push(
					conversionResult.convertedValue as CCCustomFieldValue,
				);
				result.fieldsUpdated++;

				if (conversionResult.warnings) {
					result.warnings.push(...conversionResult.warnings);
				}
			} else {
				result.fieldsFailed++;
				if (conversionResult.error) {
					result.errors.push(
						`Field ${mapping.ccConfig.name}: ${conversionResult.error}`,
					);
				}
			}
		}

		console.log(
			"fieldResults",
			JSON.stringify(fieldResults.map((r) => r.convertedValue)),
		);

		result.results = fieldResults;

		// Step 4: Update CC patient if we have converted fields
		if (ccCustomFields.length > 0) {
			// Get CC patient ID from lookup
			const ccPatientData = await fetchCcPatientData(patientId);
			if (!ccPatientData) {
				result.errors.push("Failed to get CC patient ID for update");
				result.success = false;
				result.executionTimeMs = Date.now() - startTime;
				return result;
			}

			const updateSuccess = await updateCcPatientCustomFields(
				ccPatientData.id as number,
				ccCustomFields,
			);

			if (updateSuccess) {
				result.success = true;
				logInfo("Successfully synchronized AP to CC custom fields", {
					patientId,
					fieldsUpdated: result.fieldsUpdated,
					fieldsSkipped: result.fieldsSkipped,
					fieldsFailed: result.fieldsFailed,
				});
			} else {
				result.errors.push("Failed to update CC patient custom fields");
				result.success = false;
			}
		} else {
			result.success = true;
			logInfo("No fields to update in CC", {
				patientId,
				fieldsSkipped: result.fieldsSkipped,
				fieldsFailed: result.fieldsFailed,
			});
		}
	} catch (error) {
		logError("Error during AP to CC synchronization", {
			patientId,
			error: String(error),
		});
		result.errors.push(`Synchronization error: ${String(error)}`);
		result.success = false;
	}

	result.executionTimeMs = Date.now() - startTime;
	return result;
}

/**
 * Synchronize custom field values from CliniCore to AutoPatient
 *
 * Fetches patient data from CC, converts custom field values using field mappings,
 * and updates the corresponding contact in AP with the converted values.
 *
 * @param patientId - Local database patient ID
 * @param skipMissingFields - Whether to skip fields without mappings
 * @returns Synchronization result with detailed statistics
 */
export async function syncCcToApCustomFields(
	patientId: string,
	skipMissingFields = true,
): Promise<PatientCustomFieldSyncResult> {
	const startTime = Date.now();

	logInfo("Starting CC to AP custom field value synchronization", {
		patientId,
		skipMissingFields,
	});

	const result: PatientCustomFieldSyncResult = {
		success: false,
		patientId,
		targetPlatform: "ap",
		fieldsProcessed: 0,
		fieldsUpdated: 0,
		fieldsSkipped: 0,
		fieldsFailed: 0,
		results: [],
		errors: [],
		warnings: [],
		executionTimeMs: 0,
	};

	try {
		// Step 1: Fetch patient data from CC
		const ccPatientData = await fetchCcPatientData(patientId);
		if (!ccPatientData) {
			result.errors.push("Failed to fetch patient data from CliniCore");
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		const ccCustomFields = ccPatientData.customFields as CCCustomFieldValue[];
		if (!ccCustomFields || ccCustomFields.length === 0) {
			logInfo("No custom fields found for CC patient", {
				patientId,
			});
			result.success = true;
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		// Step 2: Get field mappings
		const fieldMappings = await getAllFieldMappings();
		if (fieldMappings.length === 0) {
			const error = "No field mappings found in database";
			result.errors.push(error);
			logWarn(error, { patientId });
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		const { ccToAP } = createMappingLookupTables(fieldMappings);

		// Step 3: Convert CC values to AP format
		const apCustomFields: APCustomFieldValue[] = [];
		const fieldResults: FieldValueSyncResult[] = [];

		for (const ccField of ccCustomFields) {
			result.fieldsProcessed++;

			const mapping = ccToAP.get(ccField.field.id);
			if (!mapping) {
				const fieldResult: FieldValueSyncResult = {
					fieldName: ccField.field.name,
					ccFieldId: ccField.field.id,
					success: false,
					action: skipMissingFields ? "skipped" : "failed",
					originalValue: ccField.values,
					error: skipMissingFields
						? "No field mapping found"
						: "Missing field mapping",
				};

				fieldResults.push(fieldResult);

				if (skipMissingFields) {
					result.fieldsSkipped++;
				} else {
					result.fieldsFailed++;
					result.errors.push(
						`No mapping found for CC field ${ccField.field.id}`,
					);
				}
				continue;
			}

			// Convert value
			const conversionResult = convertCcValueToAp(ccField, mapping);

			const fieldResult: FieldValueSyncResult = {
				fieldName: mapping.apConfig.name,
				ccFieldId: ccField.field.id,
				apFieldId: mapping.apId,
				success: conversionResult.success,
				action: conversionResult.success ? "updated" : "failed",
				originalValue: ccField.values,
				convertedValue: conversionResult.convertedValue
					? (conversionResult.convertedValue as APCustomFieldValue).value
					: null,
				error: conversionResult.error,
				warnings: conversionResult.warnings,
			};

			fieldResults.push(fieldResult);

			if (conversionResult.success && conversionResult.convertedValue) {
				apCustomFields.push(
					conversionResult.convertedValue as APCustomFieldValue,
				);
				result.fieldsUpdated++;

				if (conversionResult.warnings) {
					result.warnings.push(...conversionResult.warnings);
				}
			} else {
				result.fieldsFailed++;
				if (conversionResult.error) {
					result.errors.push(
						`Field ${mapping.apConfig.name}: ${conversionResult.error}`,
					);
				}
			}
		}

		result.results = fieldResults;

		// Step 4: Update AP contact if we have converted fields
		if (apCustomFields.length > 0) {
			// Get AP contact ID from lookup
			const apPatientData = await fetchApPatientData(patientId);
			if (!apPatientData) {
				result.errors.push("Failed to get AP contact ID for update");
				result.success = false;
				result.executionTimeMs = Date.now() - startTime;
				return result;
			}

			const updateSuccess = await updateApPatientCustomFields(
				apPatientData.id as string,
				apCustomFields,
			);

			if (updateSuccess) {
				result.success = true;
				logInfo("Successfully synchronized CC to AP custom fields", {
					patientId,
					fieldsUpdated: result.fieldsUpdated,
					fieldsSkipped: result.fieldsSkipped,
					fieldsFailed: result.fieldsFailed,
				});
			} else {
				result.errors.push("Failed to update AP contact custom fields");
				result.success = false;
			}
		} else {
			result.success = true;
			logInfo("No fields to update in AP", {
				patientId,
				fieldsSkipped: result.fieldsSkipped,
				fieldsFailed: result.fieldsFailed,
			});
		}
	} catch (error) {
		logError("Error during CC to AP synchronization", {
			patientId,
			error: String(error),
		});
		result.errors.push(`Synchronization error: ${String(error)}`);
		result.success = false;
	}

	result.executionTimeMs = Date.now() - startTime;
	return result;
}

// Re-export types for convenience
export type * from "./types";
