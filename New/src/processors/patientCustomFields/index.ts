/**
 * Patient Custom Field Value Synchronization
 *
 * Main entry point for patient custom field value synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Provides the core functions
 * used by the admin endpoint for bidirectional value synchronization.
 *
 * @fileoverview Patient custom field value synchronization main functions
 * @version 1.0.0
 * @since 2024-07-29
 */

import { logError, logInfo, logWarn, logDebug } from "@/utils/logger";
import {
	createMappingLookupTables,
	getAllFieldMappings,
} from "./fieldMappingResolver";
import {
	fetchApPatientData,
	fetchCcPatientData,
	updateApPatientCustomFields,
	updateCcPatientCustomFields,
} from "./patientDataFetcher";
import type {
	APCustomFieldValue,
	CCCustomFieldValue,
	FieldValueSyncResult,
	PatientCustomFieldSyncResult,
} from "./types";
import { convertApValueToCc, convertCcValueToAp, convertApStandardValueToCc } from "./valueConverters";
import { getStandardToCustomMappings } from "@/processors/customFields/database/operations";
import apiClient from "@apiClient";

/**
 * Synchronize custom field values from AutoPatient to CliniCore
 *
 * Fetches patient data from AP, converts custom field values using field mappings,
 * and updates the corresponding patient in CC with the converted values.
 *
 * @param patientId - Local database patient ID
 * @param skipMissingFields - Whether to skip fields without mappings
 * @returns Synchronization result with detailed statistics
 */
export async function syncApToCcCustomFields(
	patientId: string,
	skipMissingFields = true,
): Promise<PatientCustomFieldSyncResult> {
	const startTime = Date.now();

	const result: PatientCustomFieldSyncResult = {
		success: false,
		patientId,
		targetPlatform: "cc",
		fieldsProcessed: 0,
		fieldsUpdated: 0,
		fieldsSkipped: 0,
		fieldsFailed: 0,
		results: [],
		errors: [],
		warnings: [],
		executionTimeMs: 0,
	};

	try {
		// Step 1: Fetch patient data from AP
		const apPatientData = await fetchApPatientData(patientId);
		if (!apPatientData) {
			result.errors.push("Failed to fetch patient data from AutoPatient");
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		const apCustomFields = apPatientData.customFields as APCustomFieldValue[];
		if (!apCustomFields || apCustomFields.length === 0) {
			logInfo("No custom fields found for AP patient", {
				patientId,
			});
			result.success = true;
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		// Step 2: Get field mappings
		const fieldMappings = await getAllFieldMappings();
		if (fieldMappings.length === 0) {
			const error = "No field mappings found in database";
			result.errors.push(error);
			logWarn(error, { patientId });
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		const { apToCC } = createMappingLookupTables(fieldMappings);

		// Step 3: Convert AP values to CC format
		const ccCustomFields: CCCustomFieldValue[] = [];
		const fieldResults: FieldValueSyncResult[] = [];

		for (const apField of apCustomFields) {
			result.fieldsProcessed++;

			const mapping = apToCC.get(apField.id);
			if (!mapping) {
				const fieldResult: FieldValueSyncResult = {
					fieldName: `AP Field ${apField.id}`,
					apFieldId: apField.id,
					success: false,
					action: skipMissingFields ? "skipped" : "failed",
					originalValue: apField.value,
					error: skipMissingFields
						? "No field mapping found"
						: "Missing field mapping",
				};

				fieldResults.push(fieldResult);

				if (skipMissingFields) {
					result.fieldsSkipped++;
				} else {
					result.fieldsFailed++;
					result.errors.push(`No mapping found for AP field ${apField.id}`);
				}
				continue;
			}

			// Convert value
			const conversionResult = convertApValueToCc(apField, mapping);

			const fieldResult: FieldValueSyncResult = {
				fieldName: mapping.ccConfig.name,
				apFieldId: apField.id,
				ccFieldId: mapping.ccId,
				success: conversionResult.success,
				action: conversionResult.success ? "updated" : "failed",
				originalValue: apField.value,
				convertedValue: conversionResult.convertedValue
					? (conversionResult.convertedValue as CCCustomFieldValue).values
					: null,
				error: conversionResult.error,
				warnings: conversionResult.warnings,
			};

			fieldResults.push(fieldResult);

			if (conversionResult.success && conversionResult.convertedValue) {
				ccCustomFields.push(
					conversionResult.convertedValue as CCCustomFieldValue,
				);
				result.fieldsUpdated++;

				if (conversionResult.warnings) {
					result.warnings.push(...conversionResult.warnings);
				}
			} else {
				result.fieldsFailed++;
				if (conversionResult.error) {
					result.errors.push(
						`Field ${mapping.ccConfig.name}: ${conversionResult.error}`,
					);
				}
			}
		}

		console.log(
			"fieldResults",
			JSON.stringify(fieldResults.map((r) => r.convertedValue)),
		);

		result.results = fieldResults;

		// Step 4: Update CC patient if we have converted fields
		if (ccCustomFields.length > 0) {
			// Get CC patient ID from lookup
			const ccPatientData = await fetchCcPatientData(patientId);
			if (!ccPatientData) {
				result.errors.push("Failed to get CC patient ID for update");
				result.success = false;
				result.executionTimeMs = Date.now() - startTime;
				return result;
			}

			const updateSuccess = await updateCcPatientCustomFields(
				ccPatientData.id as number,
				ccCustomFields,
			);

			if (updateSuccess) {
				result.success = true;
				logInfo("Successfully synchronized AP to CC custom fields", {
					patientId,
					fieldsUpdated: result.fieldsUpdated,
					fieldsSkipped: result.fieldsSkipped,
					fieldsFailed: result.fieldsFailed,
				});
			} else {
				result.errors.push("Failed to update CC patient custom fields");
				result.success = false;
			}
		} else {
			result.success = true;
			logInfo("No fields to update in CC", {
				patientId,
				fieldsSkipped: result.fieldsSkipped,
				fieldsFailed: result.fieldsFailed,
			});
		}
	} catch (error) {
		logError("Error during AP to CC synchronization", {
			patientId,
			error: String(error),
		});
		result.errors.push(`Synchronization error: ${String(error)}`);
		result.success = false;
	}

	result.executionTimeMs = Date.now() - startTime;
	return result;
}

/**
 * Synchronize custom field values from CliniCore to AutoPatient
 *
 * Fetches patient data from CC, converts custom field values using field mappings,
 * and updates the corresponding contact in AP with the converted values.
 *
 * @param patientId - Local database patient ID
 * @param skipMissingFields - Whether to skip fields without mappings
 * @returns Synchronization result with detailed statistics
 */
export async function syncCcToApCustomFields(
	patientId: string,
	skipMissingFields = true,
): Promise<PatientCustomFieldSyncResult> {
	const startTime = Date.now();

	logInfo("Starting CC to AP custom field value synchronization", {
		patientId,
		skipMissingFields,
	});

	const result: PatientCustomFieldSyncResult = {
		success: false,
		patientId,
		targetPlatform: "ap",
		fieldsProcessed: 0,
		fieldsUpdated: 0,
		fieldsSkipped: 0,
		fieldsFailed: 0,
		results: [],
		errors: [],
		warnings: [],
		executionTimeMs: 0,
	};

	try {
		// Step 1: Fetch patient data from CC
		const ccPatientData = await fetchCcPatientData(patientId);
		if (!ccPatientData) {
			result.errors.push("Failed to fetch patient data from CliniCore");
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		const ccCustomFields = ccPatientData.customFields as CCCustomFieldValue[];
		if (!ccCustomFields || ccCustomFields.length === 0) {
			logInfo("No custom fields found for CC patient", {
				patientId,
			});
			result.success = true;
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		// Step 2: Get field mappings
		const fieldMappings = await getAllFieldMappings();
		if (fieldMappings.length === 0) {
			const error = "No field mappings found in database";
			result.errors.push(error);
			logWarn(error, { patientId });
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		const { ccToAP } = createMappingLookupTables(fieldMappings);

		// Step 3: Convert CC values to AP format
		const apCustomFields: APCustomFieldValue[] = [];
		const fieldResults: FieldValueSyncResult[] = [];

		for (const ccField of ccCustomFields) {
			result.fieldsProcessed++;

			const mapping = ccToAP.get(ccField.field.id);
			if (!mapping) {
				const fieldResult: FieldValueSyncResult = {
					fieldName: ccField.field.name,
					ccFieldId: ccField.field.id,
					success: false,
					action: skipMissingFields ? "skipped" : "failed",
					originalValue: ccField.values,
					error: skipMissingFields
						? "No field mapping found"
						: "Missing field mapping",
				};

				fieldResults.push(fieldResult);

				if (skipMissingFields) {
					result.fieldsSkipped++;
				} else {
					result.fieldsFailed++;
					result.errors.push(
						`No mapping found for CC field ${ccField.field.id}`,
					);
				}
				continue;
			}

			// Convert value
			const conversionResult = convertCcValueToAp(ccField, mapping);

			const fieldResult: FieldValueSyncResult = {
				fieldName: mapping.apConfig.name,
				ccFieldId: ccField.field.id,
				apFieldId: mapping.apId,
				success: conversionResult.success,
				action: conversionResult.success ? "updated" : "failed",
				originalValue: ccField.values,
				convertedValue: conversionResult.convertedValue
					? (conversionResult.convertedValue as APCustomFieldValue).value
					: null,
				error: conversionResult.error,
				warnings: conversionResult.warnings,
			};

			fieldResults.push(fieldResult);

			if (conversionResult.success && conversionResult.convertedValue) {
				apCustomFields.push(
					conversionResult.convertedValue as APCustomFieldValue,
				);
				result.fieldsUpdated++;

				if (conversionResult.warnings) {
					result.warnings.push(...conversionResult.warnings);
				}
			} else {
				result.fieldsFailed++;
				if (conversionResult.error) {
					result.errors.push(
						`Field ${mapping.apConfig.name}: ${conversionResult.error}`,
					);
				}
			}
		}

		result.results = fieldResults;

		// Step 4: Update AP contact if we have converted fields
		if (apCustomFields.length > 0) {
			// Get AP contact ID from lookup
			const apPatientData = await fetchApPatientData(patientId);
			if (!apPatientData) {
				result.errors.push("Failed to get AP contact ID for update");
				result.success = false;
				result.executionTimeMs = Date.now() - startTime;
				return result;
			}

			const updateSuccess = await updateApPatientCustomFields(
				apPatientData.id as string,
				apCustomFields,
			);

			if (updateSuccess) {
				result.success = true;
				logInfo("Successfully synchronized CC to AP custom fields", {
					patientId,
					fieldsUpdated: result.fieldsUpdated,
					fieldsSkipped: result.fieldsSkipped,
					fieldsFailed: result.fieldsFailed,
				});
			} else {
				result.errors.push("Failed to update AP contact custom fields");
				result.success = false;
			}
		} else {
			result.success = true;
			logInfo("No fields to update in AP", {
				patientId,
				fieldsSkipped: result.fieldsSkipped,
				fieldsFailed: result.fieldsFailed,
			});
		}
	} catch (error) {
		logError("Error during CC to AP synchronization", {
			patientId,
			error: String(error),
		});
		result.errors.push(`Synchronization error: ${String(error)}`);
		result.success = false;
	}

	result.executionTimeMs = Date.now() - startTime;
	return result;
}

/**
 * Synchronize AP standard field values to CC custom fields
 *
 * Fetches patient data from AP, extracts standard field values, converts them
 * using standard-to-custom field mappings, and updates the corresponding CC
 * patient with the converted custom field values.
 *
 * @param patientId - Local database patient ID
 * @returns Synchronization result with detailed statistics
 *
 * @example
 * ```typescript
 * const result = await syncApStandardToCcCustomFields("patient-123");
 * if (result.success) {
 *   console.log(`Synchronized ${result.fieldsUpdated} standard fields`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function syncApStandardToCcCustomFields(
	patientId: string,
): Promise<PatientCustomFieldSyncResult> {
	const startTime = Date.now();

	const result: PatientCustomFieldSyncResult = {
		success: false,
		patientId,
		targetPlatform: "cc",
		fieldsProcessed: 0,
		fieldsUpdated: 0,
		fieldsSkipped: 0,
		fieldsFailed: 0,
		results: [],
		errors: [],
		warnings: [],
		executionTimeMs: 0,
	};

	try {
		logInfo("Starting AP standard to CC custom field synchronization", {
			patientId,
		});

		// Step 1: Get standard-to-custom field mappings
		const standardMappings = await getStandardToCustomMappings();
		if (standardMappings.length === 0) {
			logInfo("No standard-to-custom field mappings configured", {
				patientId,
			});
			result.success = true;
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		logDebug(`Found ${standardMappings.length} standard-to-custom mappings`);

		// Step 2: Fetch AP patient data
		const apPatientData = await fetchApPatientData(patientId);
		if (!apPatientData) {
			result.errors.push("Failed to fetch patient data from AutoPatient");
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		// Step 3: Extract AP contact data from patient record
		const apContact = apPatientData.apData;
		if (!apContact) {
			result.errors.push("No AP contact data found in patient record");
			result.executionTimeMs = Date.now() - startTime;
			return result;
		}

		// Step 4: Process each standard field mapping
		const ccCustomFields: CCCustomFieldValue[] = [];
		const fieldResults: FieldValueSyncResult[] = [];

		for (const mapping of standardMappings) {
			result.fieldsProcessed++;

			const { apStandardField, ccConfig } = mapping;

			// Extract standard field value from AP contact
			const apValue = extractApStandardFieldValue(apContact, apStandardField);

			const fieldResult: FieldValueSyncResult = {
				fieldName: ccConfig!.name,
				ccFieldId: ccConfig!.id,
				success: false,
				action: "skipped",
				originalValue: apValue,
			};

			// Skip if no value present
			if (apValue === null || apValue === undefined || apValue === "") {
				fieldResult.action = "skipped";
				fieldResults.push(fieldResult);
				result.fieldsSkipped++;
				continue;
			}

			try {
				// Convert AP standard field value to CC custom field format
				const conversionResult = convertApStandardValueToCc(
					apStandardField,
					apValue,
					ccConfig!,
				);

				fieldResult.success = conversionResult.success;
				fieldResult.action = conversionResult.success ? "updated" : "failed";
				fieldResult.convertedValue = conversionResult.convertedValue
					? (conversionResult.convertedValue as CCCustomFieldValue).values
					: null;
				fieldResult.error = conversionResult.error;
				fieldResult.warnings = conversionResult.warnings;

				if (conversionResult.success && conversionResult.convertedValue) {
					ccCustomFields.push(
						conversionResult.convertedValue as CCCustomFieldValue,
					);
					result.fieldsUpdated++;

					if (conversionResult.warnings) {
						result.warnings.push(...conversionResult.warnings);
					}
				} else {
					result.fieldsFailed++;
					if (conversionResult.error) {
						result.errors.push(
							`Failed to convert ${apStandardField}: ${conversionResult.error}`,
						);
					}
				}
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : String(error);
				fieldResult.success = false;
				fieldResult.action = "failed";
				fieldResult.error = errorMessage;
				result.fieldsFailed++;
				result.errors.push(
					`Error processing ${apStandardField}: ${errorMessage}`,
				);
			}

			fieldResults.push(fieldResult);
		}

		result.results = fieldResults;

		// Step 5: Update CC patient if we have custom field updates
		if (ccCustomFields.length > 0) {
			const ccPatientData = await fetchCcPatientData(patientId);
			if (!ccPatientData) {
				result.errors.push("Failed to fetch patient data from CliniCore");
				result.executionTimeMs = Date.now() - startTime;
				return result;
			}

			const updateResult = await updateCcPatientCustomFields(
				ccPatientData.ccId!,
				ccCustomFields,
			);

			if (!updateResult.success) {
				result.errors.push(
					`Failed to update CC patient: ${updateResult.error}`,
				);
				result.executionTimeMs = Date.now() - startTime;
				return result;
			}

			logInfo("Successfully updated CC patient with standard field values", {
				patientId,
				ccPatientId: ccPatientData.ccId,
				fieldsUpdated: result.fieldsUpdated,
			});
		}

		result.success = true;
		logInfo("AP standard to CC custom field synchronization completed", {
			patientId,
			fieldsProcessed: result.fieldsProcessed,
			fieldsUpdated: result.fieldsUpdated,
			fieldsSkipped: result.fieldsSkipped,
			fieldsFailed: result.fieldsFailed,
		});

	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		result.errors.push(`Synchronization failed: ${errorMessage}`);
		logError("AP standard to CC custom field synchronization failed", {
			patientId,
			error: errorMessage,
		});
	}

	result.executionTimeMs = Date.now() - startTime;
	return result;
}

/**
 * Extract value from AP contact data for a given standard field
 *
 * @param apContact - AutoPatient contact data
 * @param fieldName - Standard field name to extract
 * @returns Field value or null if not found
 */
function extractApStandardFieldValue(
	apContact: Record<string, unknown>,
	fieldName: string,
): unknown {
	// Map of standard field names to contact properties
	const fieldMap: Record<string, string> = {
		email: "email",
		phone: "phone",
		firstName: "firstName",
		lastName: "lastName",
		name: "name",
		dateOfBirth: "dateOfBirth",
		gender: "gender",
		source: "source",
		country: "country",
		address1: "address1",
		city: "city",
		state: "state",
		postalCode: "postalCode",
		companyName: "companyName",
		tags: "tags",
	};

	const contactProperty = fieldMap[fieldName];
	if (contactProperty && contactProperty in apContact) {
		return apContact[contactProperty];
	}

	logDebug(`Standard field not found in contact data: ${fieldName}`);
	return null;
}

// Re-export types for convenience
export type * from "./types";
